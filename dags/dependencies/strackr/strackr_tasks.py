"""
Simplified Strackr DAG task functions.
Contains all the logic for fetching, transforming, and uploading Strackr transaction data.
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, List
from supabase import create_client, Client

from .strackr_client import StrackrClient, get_yesterday_date_range
from .models import StrackrTransaction, transform_strackr_data

logger = logging.getLogger(__name__)


def fetch_strackr_transactions(**context) -> Dict[str, Any]:
    """
    Fetch transactions from Strackr API for yesterday.
    
    Returns:
        Dictionary with fetch results and transaction data
    """
    logger.info("Starting Strackr transaction fetch")
    
    try:
        # Get yesterday's date range
        start_date, end_date = get_yesterday_date_range()
        logger.info(f"Fetching transactions for {start_date}")
        
        # Fetch transactions
        client = StrackrClient()
        raw_transactions = client.get_transactions(start_date, end_date)
        
        result = {
            "success": True,
            "transaction_count": len(raw_transactions),
            "date_range": {"start": start_date, "end": end_date},
            "raw_transactions": raw_transactions
        }
        
        logger.info(f"Successfully fetched {len(raw_transactions)} transactions")
        return result
        
    except Exception as e:
        logger.error(f"Failed to fetch transactions: {e}")
        return {
            "success": False,
            "error": str(e),
            "transaction_count": 0
        }


def transform_strackr_transactions(**context) -> Dict[str, Any]:
    """
    Transform raw Strackr transactions to normalized format.
    
    Returns:
        Dictionary with transformation results
    """
    logger.info("Starting Strackr transaction transformation")
    
    try:
        # Get raw transactions from previous task
        fetch_result = context["task_instance"].xcom_pull(task_ids="fetch_strackr_transactions")
        
        if not fetch_result["success"]:
            raise Exception(f"Fetch task failed: {fetch_result.get('error', 'Unknown error')}")
        
        raw_transactions = fetch_result["raw_transactions"]
        logger.info(f"Transforming {len(raw_transactions)} transactions")
        
        # Transform transactions
        transformed_transactions = []
        failed_count = 0
        
        for raw_tx in raw_transactions:
            transformed = transform_strackr_data(raw_tx)
            if transformed:
                transformed_transactions.append(transformed)
            else:
                failed_count += 1
        
        result = {
            "success": True,
            "total_raw": len(raw_transactions),
            "transformed_count": len(transformed_transactions),
            "failed_count": failed_count,
            "transactions": [tx.to_dict() for tx in transformed_transactions]
        }
        
        logger.info(f"Transformation complete: {len(transformed_transactions)} valid, {failed_count} failed")
        return result
        
    except Exception as e:
        logger.error(f"Failed to transform transactions: {e}")
        return {
            "success": False,
            "error": str(e),
            "transformed_count": 0
        }


def upload_to_supabase(**context) -> Dict[str, Any]:
    """
    Upload transformed transactions to Supabase.
    
    Returns:
        Dictionary with upload results
    """
    logger.info("Starting Supabase upload")
    
    try:
        # Get transformed transactions from previous task
        transform_result = context["task_instance"].xcom_pull(task_ids="transform_strackr_transactions")
        
        if not transform_result["success"]:
            raise Exception(f"Transform task failed: {transform_result.get('error', 'Unknown error')}")
        
        transactions = transform_result["transactions"]
        if not transactions:
            logger.info("No transactions to upload")
            return {"success": True, "uploaded_count": 0, "message": "No transactions to upload"}
        
        logger.info(f"Uploading {len(transactions)} transactions to Supabase")
        
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables required")
        
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Upload in batches
        batch_size = 100
        total_uploaded = 0
        errors = []
        
        for i in range(0, len(transactions), batch_size):
            batch = transactions[i:i + batch_size]
            
            try:
                # Use upsert to handle duplicates
                result = supabase.table("normalized_transactions").upsert(
                    batch, 
                    on_conflict="transaction_id"
                ).execute()
                
                batch_uploaded = len(result.data) if result.data else len(batch)
                total_uploaded += batch_uploaded
                logger.info(f"Uploaded batch {i//batch_size + 1}: {batch_uploaded} transactions")
                
            except Exception as e:
                error_msg = f"Batch {i//batch_size + 1} failed: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        result = {
            "success": len(errors) == 0,
            "total_transactions": len(transactions),
            "uploaded_count": total_uploaded,
            "error_count": len(errors),
            "errors": errors
        }
        
        logger.info(f"Upload complete: {total_uploaded}/{len(transactions)} transactions uploaded")
        return result
        
    except Exception as e:
        logger.error(f"Failed to upload to Supabase: {e}")
        return {
            "success": False,
            "error": str(e),
            "uploaded_count": 0
        }
