#!/usr/bin/env python3
"""
Strackr DAG Local Test Script

This script simulates running the entire Strackr DAG locally to test:
1. Data fetching from Strackr API
2. Data transformation to normalized schema
3. Data upload to Supabase normalized_transactions table
4. Verification of data in Supabase

Run this before deploying to Airflow to ensure everything works end-to-end.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dags', 'dependencies'))

from strackr.strackr_tasks import (
    fetch_strackr_transactions,
    transform_strackr_transactions,
    upload_to_supabase
)

def create_mock_airflow_context(execution_date=None):
    """Create a mock Airflow context for testing."""
    if execution_date is None:
        execution_date = datetime.now()
    
    class MockTaskInstance:
        def __init__(self):
            self.xcom_data = {}
        
        def xcom_pull(self, task_ids):
            return self.xcom_data.get(task_ids)
        
        def xcom_push(self, key, value):
            self.xcom_data[key] = value
    
    ti = MockTaskInstance()
    
    return {
        'execution_date': execution_date,
        'ti': ti,
        'ds': execution_date.strftime('%Y-%m-%d'),
        'ds_nodash': execution_date.strftime('%Y%m%d'),
        'task_instance': ti
    }

def test_supabase_connection():
    """Test connection to Supabase and verify normalized_transactions table exists."""
    print("🔍 Testing Supabase connection...")
    
    try:
        # Import Supabase client
        from supabase import create_client, Client
        
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_SERVICE_KEY')
        
        if not url or not key:
            print("❌ Missing Supabase credentials:")
            print("   - SUPABASE_URL")
            print("   - SUPABASE_SERVICE_KEY")
            return None
        
        supabase: Client = create_client(url, key)
        
        # Test connection by checking if normalized_transactions table exists
        try:
            result = supabase.table('normalized_transactions').select("*").limit(0).execute()
            print("   ✅ Table 'normalized_transactions' exists")
            print("✅ Supabase connection successful")
            return supabase
        except Exception as e:
            print(f"   ❌ Table 'normalized_transactions' missing or inaccessible: {str(e)[:100]}...")
            print("💡 Make sure the normalized_transactions table exists in Supabase")
            return None
            
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return None

def test_strackr_credentials():
    """Test Strackr API credentials."""
    print("🔍 Testing Strackr API credentials...")
    
    strackr_api_id = os.getenv('STRACKR_API_ID')
    strackr_api_key = os.getenv('STRACKR_API_KEY')
    
    if not strackr_api_id or not strackr_api_key:
        print("❌ Missing Strackr credentials:")
        print("   - STRACKR_API_ID")
        print("   - STRACKR_API_KEY")
        print("💡 Add these to your .env file")
        return False
    
    print("✅ Strackr credentials found")
    return True

def verify_data_in_supabase(supabase_client, target_date):
    """Verify that data was correctly inserted into Supabase."""
    print(f"\n🔍 Verifying data in Supabase for {target_date}...")
    
    try:
        # Query transactions for the target date
        result = supabase_client.table('normalized_transactions').select("*").eq('platform', 'strackr').execute()
        
        if not result.data:
            print("   ⚠️ No Strackr transactions found in database")
            return {'record_count': 0}
        
        # Filter for today's transactions (since we might have historical data)
        today_transactions = [
            tx for tx in result.data 
            if tx.get('transaction_date', '').startswith(target_date)
        ]
        
        total_count = len(result.data)
        today_count = len(today_transactions)
        
        print(f"   📊 Total Strackr transactions in DB: {total_count}")
        print(f"   📊 Transactions for {target_date}: {today_count}")
        
        if today_transactions:
            sample = today_transactions[0]
            print(f"   🔍 Sample transaction ID: {sample.get('transaction_id', 'N/A')}")
            print(f"   🔍 Sample order amount: ${sample.get('order_amount', 0)}")
            print(f"   🔍 Sample status: {sample.get('status', 'N/A')}")
        
        return {
            'record_count': today_count,
            'total_records': total_count,
            'sample_data': today_transactions[:2] if today_transactions else []
        }
        
    except Exception as e:
        print(f"   ❌ Verification failed: {str(e)[:100]}...")
        return {'error': str(e), 'record_count': 0}

def run_strackr_dag_test():
    """Run the complete Strackr DAG test locally."""
    print("🚀 Starting Strackr DAG Local Test")
    print("=" * 70)
    
    # Setup
    execution_date = datetime.now()
    target_date = (execution_date - timedelta(days=1)).strftime('%Y-%m-%d')
    context = create_mock_airflow_context(execution_date)
    
    print(f"📅 Target date: {target_date}")
    print(f"🕐 Execution time: {execution_date.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test credentials first
    if not test_strackr_credentials():
        print("❌ Cannot proceed without Strackr credentials")
        return {'success': False, 'error': 'Missing Strackr credentials'}
    
    # Test Supabase connection
    supabase_client = test_supabase_connection()
    if not supabase_client:
        print("❌ Cannot proceed without Supabase connection")
        return {'success': False, 'error': 'Supabase connection failed'}
    
    try:
        # Step 1: Fetch Strackr Transactions
        print(f"\n1️⃣ STEP 1: Fetching Strackr Transactions")
        print("-" * 50)
        
        start_time = time.time()
        fetch_result = fetch_strackr_transactions(**context)
        fetch_time = time.time() - start_time
        
        # Store in mock XCom
        context['ti'].xcom_push('fetch_strackr_transactions', fetch_result)
        
        if not fetch_result.get('success'):
            raise Exception(f"Fetch failed: {fetch_result.get('error', 'Unknown error')}")
        
        print(f"✅ Fetch completed in {fetch_time:.1f} seconds")
        print(f"📊 Transactions fetched: {fetch_result.get('transaction_count', 0)}")
        print(f"📅 Date range: {fetch_result.get('date_range', {})}")
        
        # Step 2: Transform Transactions
        print(f"\n2️⃣ STEP 2: Transforming Transactions")
        print("-" * 50)
        
        start_time = time.time()
        transform_result = transform_strackr_transactions(**context)
        transform_time = time.time() - start_time
        
        # Store in mock XCom
        context['ti'].xcom_push('transform_strackr_transactions', transform_result)
        
        if not transform_result.get('success'):
            raise Exception(f"Transform failed: {transform_result.get('error', 'Unknown error')}")
        
        print(f"✅ Transform completed in {transform_time:.1f} seconds")
        print(f"📊 Raw transactions: {transform_result.get('total_raw', 0)}")
        print(f"📈 Transformed: {transform_result.get('transformed_count', 0)}")
        print(f"❌ Failed: {transform_result.get('failed_count', 0)}")
        
        # Step 3: Upload to Supabase
        print(f"\n3️⃣ STEP 3: Uploading to Supabase")
        print("-" * 50)
        
        start_time = time.time()
        upload_result = upload_to_supabase(**context)
        upload_time = time.time() - start_time
        
        if not upload_result.get('success'):
            print(f"⚠️ Upload had issues: {upload_result.get('error', 'Unknown error')}")
        
        print(f"✅ Upload completed in {upload_time:.1f} seconds")
        print(f"📊 Total transactions: {upload_result.get('total_transactions', 0)}")
        print(f"📈 Uploaded: {upload_result.get('uploaded_count', 0)}")
        print(f"❌ Errors: {upload_result.get('error_count', 0)}")
        
        # Step 4: Verify Data
        print(f"\n4️⃣ STEP 4: Verifying Data in Supabase")
        print("-" * 50)
        
        verification_result = verify_data_in_supabase(supabase_client, target_date)
        
        # Final Summary
        print(f"\n" + "=" * 70)
        print("📋 STRACKR DAG TEST SUMMARY")
        print("=" * 70)
        
        total_time = fetch_time + transform_time + upload_time
        print(f"⏱️  Total execution time: {total_time:.1f} seconds")
        print(f"📊 Transactions fetched: {fetch_result.get('transaction_count', 0)}")
        print(f"📈 Transactions transformed: {transform_result.get('transformed_count', 0)}")
        print(f"📤 Transactions uploaded: {upload_result.get('uploaded_count', 0)}")
        print(f"🔍 Transactions verified: {verification_result.get('record_count', 0)}")
        
        success = (
            fetch_result.get('success', False) and
            transform_result.get('success', False) and
            upload_result.get('uploaded_count', 0) > 0
        )
        
        if success:
            print("\n🎉 STRACKR DAG TEST PASSED! ✅")
            print("💡 The pipeline is ready for Airflow deployment")
        else:
            print("\n⚠️ STRACKR DAG TEST HAD ISSUES")
            print("🔧 Review the errors above before deploying")
        
        return {
            'success': success,
            'execution_time': total_time,
            'transactions_processed': transform_result.get('transformed_count', 0),
            'transactions_uploaded': upload_result.get('uploaded_count', 0),
            'verification_result': verification_result
        }
        
    except Exception as e:
        print(f"\n❌ DAG TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Install required packages if missing
    try:
        import supabase
        import pydantic
        import requests
    except ImportError:
        print("📦 Installing required packages...")
        os.system("pip install supabase pydantic requests")
    
    # Run the test
    results = run_strackr_dag_test()
    
    # Exit with appropriate code
    exit(0 if results.get('success') else 1)
